import { ComplaintFiltersModal, ComplaintResults, ComplaintsLayout, complaints$ } from '@mass/components/dashboard'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'

function Complaints() {
  return (
    <>
      <ComplaintsLayout>
        <div
          className={clsx(
            'flex flex-col', // flex
            'gap-8 p-8 md:p-16', // spacing
          )}>
          <ComplaintResults />
        </div>
      </ComplaintsLayout>
      <ComplaintFiltersModal />
    </>
  )
}

export const Route = createFileRoute('/_common/complaints-requests')({
  async beforeLoad(ctx) {
    if (ctx.cause === 'stay') {
      return
    }

    await complaints$.fetch()
  },

  component: Complaints,
})
