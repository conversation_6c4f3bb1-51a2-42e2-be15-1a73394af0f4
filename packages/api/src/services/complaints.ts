import { type } from 'arktype'
import { apiBuilder } from '../utils/api-builder'

export const COMPLAINT_CATEGORY_BASE = type({
  'epias?': 'boolean',
  deleted: 'boolean',
  'fixed?': 'boolean',
  'edas?': 'boolean',
  label: {
    // biome-ignore lint/style/useNamingConvention: Redundant
    EN: 'string',
    // biome-ignore lint/style/useNamingConvention: Redundant
    TR: 'string',
  },
  subcategories: type({
    '*': type({
      'epias?': 'boolean',
      deleted: 'boolean',
      'fixed?': 'boolean',
      'edas?': 'boolean',
      label: {
        // biome-ignore lint/style/useNamingConvention: Redundant
        EN: 'string',
        // biome-ignore lint/style/useNamingConvention: Redundant
        TR: 'string',
      },
    }),
  }).optional(),
})

export const COMPLAINT_BASE = type({
  id: 'string.alphanumeric',
  createdAt: 'string.date',
  userId: 'string',
  subscriptionId: 'string',
  type: "'suggestion' | 'complaint'",
  subtype: 'string',
  status: "'PENDING' | 'RESOLVED' | 'CANCELED'",
  body: 'string',
  updatedAt: 'null | string.date',
  response: 'null | unknown',
  documents: [
    {
      id: 'string.alphanumeric',
      name: 'string',
      url: 'string',
    },
  ],
})

export const complaintsApi = {
  categories: apiBuilder({
    url: '/setting/global/complaints.categories',
    cache: 'until-reload',

    response: type({
      value: {
        complaint: COMPLAINT_CATEGORY_BASE,
        suggestion: COMPLAINT_CATEGORY_BASE,
      },
    }),
  }),

  list: apiBuilder({
    url: '/complaint',
    cache: 'validate',
    params: type({
      'filter:eq?': type({
        'status?': "'PENDING' | 'RESOLVED' | 'CANCELED'",
        'type?': 'string',
        'subscriptionId?': 'string',
        'subtype?': type('string'),
      }).array(),

      'pageNumber?': 'string.alphanumeric',
      'pageSize?': 'string.alphanumeric',
      'orderBy?': type("'createdAt:desc' | 'createdAt:asc'"),
    }),

    encodeParamsKeysUrl: ['filter:eq'],

    response: type({
      content: COMPLAINT_BASE.array(),
      pageable: {
        pageNumber: 'number',
        pageSize: 'number',
        sort: {
          sorted: 'boolean',
          empty: 'boolean',
          unsorted: 'boolean',
        },
        offset: 'number',
        paged: 'boolean',
        unpaged: 'boolean',
      },
      last: 'boolean',
      totalPages: 'number',
      totalElements: 'number',
      size: 'number',
      number: 'number',
      sort: {
        sorted: 'boolean',
        empty: 'boolean',
        unsorted: 'boolean',
      },
      first: 'boolean',
      numberOfElements: 'number',
      empty: 'boolean',
    }),
  }),

  cancel: apiBuilder({
    method: 'PATCH',
    url: '/complaint/$id/cancel',

    query: type({
      id: 'string',
    }),

    response: type('unknown'),
  }),
} as const
