import { use$ } from '@legendapp/state/react'
import { useLocation } from '@tanstack/react-router'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { subscription$ } from '../stores/subscription'

export const HOME_REGEX = /^\/$/
export const SUBSCRIPTION_REGEX = /^\/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}/
export const SETTINGS_REGEX = /^\/settings/
export const NOTIFICATIONS_REGEX = /^\/notifications/
export const COMPLAINTS_REQUESTS_REGEX = /^\/complaints-requests/

export const useMeta = () => {
  const { pathname } = useLocation()
  const { t: dashboard } = useTranslation('dashboard')

  const selectedSubscription = use$(() => subscription$.selectedSubscription.get())

  const routes = useMemo(() => {
    return [
      [HOME_REGEX, 'subscriptions.title', 'subscriptions.description', false],
      [
        SUBSCRIPTION_REGEX,
        dashboard('subscriptions.x', {
          name: selectedSubscription?.name ?? '',
        }),
        dashboard('subscriptions.x-detail'),
        true,
      ],
      [SETTINGS_REGEX, 'settings.title', 'settings.description', true],
      [NOTIFICATIONS_REGEX, 'notifications.title', 'notifications.description', false],
      [COMPLAINTS_REQUESTS_REGEX, 'complaints-requests.title', 'complaints-requests.description', false],
    ] as const
  }, [dashboard, selectedSubscription])

  return useMemo(() => {
    const foundRoute = routes.find(([regex]) => regex.test(pathname))

    if (!foundRoute) {
      return {
        title: null,
        description: null,
        hasTabs: false,
      }
    }

    return {
      title: dashboard(foundRoute[1]),
      description: dashboard(foundRoute[2]),
      hasTabs: foundRoute[3],
    }
  }, [pathname, dashboard, routes])
}
